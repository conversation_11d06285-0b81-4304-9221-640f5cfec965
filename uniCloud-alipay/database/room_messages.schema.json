{"bsonType": "object", "description": "房间消息记录表", "required": ["room_id", "message_type", "timestamp"], "properties": {"_id": {"description": "消息文档ID，系统自动生成"}, "room_id": {"bsonType": "string", "description": "关联的房间ID", "pattern": "^SX[0-9]{8}$", "title": "房间ID"}, "message_type": {"bsonType": "string", "description": "消息类型：score计分消息，system系统提示，settlement结算单，distribute出分消息，collect收分消息，round_start新局开始，round_end局结束，tea_score茶水计分消息", "enum": ["score", "system", "settlement", "distribute", "collect", "round_start", "round_end", "tea_score"], "title": "消息类型"}, "sender_id": {"bsonType": "string", "description": "发送者用户ID，系统消息可为空", "title": "发送者ID"}, "sender_name": {"bsonType": "string", "description": "发送者昵称", "maxLength": 20, "title": "发送者昵称"}, "sender_avatar_fileId": {"bsonType": "string", "description": "发送者头像云存储文件ID", "title": "发送者头像"}, "timestamp": {"bsonType": "timestamp", "description": "消息时间戳", "title": "消息时间"}, "detail_data": {"bsonType": "object", "description": "消息详细数据，JSON格式存储不同类型消息的具体内容", "title": "详细数据", "properties": {"target_id": {"bsonType": "string", "description": "计分消息：目标玩家ID"}, "target_name": {"bsonType": "string", "description": "计分消息：目标玩家昵称"}, "target_avatar_fileId": {"bsonType": "string", "description": "计分消息：目标玩家头像云存储文件ID"}, "amount": {"bsonType": "number", "description": "计分消息：分数金额"}, "message_text": {"bsonType": "string", "description": "系统消息：消息内容"}, "action_type": {"bsonType": "string", "description": "系统消息：操作类型"}, "details": {"bsonType": "array", "description": "结算单消息：结算详情数组"}, "total_label": {"bsonType": "string", "description": "结算单消息：总计标签"}, "total_amount": {"bsonType": "number", "description": "结算单消息：总计金额"}, "round_number": {"bsonType": "number", "description": "给分玩法消息：局数"}, "original_amount": {"bsonType": "number", "description": "收分消息：原始收分金额"}, "actual_amount": {"bsonType": "number", "description": "收分消息：实际到账金额"}, "tea_amount": {"bsonType": "number", "description": "收分消息：茶水抽取金额"}, "table_score_before": {"bsonType": "number", "description": "出分/收分消息：操作前桌面分数"}, "table_score_after": {"bsonType": "number", "description": "出分/收分消息：操作后桌面分数"}, "trigger_reason": {"bsonType": "string", "description": "局开始/结束消息：触发原因"}, "final_table_score": {"bsonType": "number", "description": "局结束消息：最终桌面分数"}}}, "create_time": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}, "title": "创建时间"}}, "permission": {"read": "auth.uid && (doc.sender_id == auth.uid || get('database').collection('rooms').where({room_id: doc.room_id, 'players.user_id': auth.uid}).count() > 0)", "create": false, "update": false, "delete": false, "count": false}, "index": [{"IndexName": "room_timestamp_composite", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "room_id", "Direction": "1"}, {"Name": "timestamp", "Direction": "-1"}], "MgoIsUnique": false}}, {"IndexName": "room_type_composite", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "room_id", "Direction": "1"}, {"Name": "message_type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sender_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sender_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "create_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "create_time", "Direction": "-1"}], "MgoIsUnique": false}}]}