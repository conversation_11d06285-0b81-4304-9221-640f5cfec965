# 茶水计分玩家分数扣减功能实现

## 功能概述
实现了茶水计分时扣减给茶水计分的玩家相应分数的功能，使茶水计分的行为与给其他玩家计分保持一致。

## 实现原理
当玩家给茶水计分时：
1. **茶水余额增加**：茶水余额增加相应分数
2. **玩家分数扣减**：给茶水计分的玩家分数减少相应分数
3. **消息记录**：记录茶水计分消息到数据库
4. **实时同步**：通过WebSocket广播给房间内其他玩家

## 修改的文件和内容

### 1. WebSocket处理函数
**文件**: `uniCloud-alipay/cloudfunctions/room-websocket/index.js`

#### 添加tea_score处理case
```javascript
case 'tea_score':
  await handleTeaScore(connectionId, data, requestId);
  actionResult.success = true;
  break;
```

#### 实现handleTeaScore函数
- 验证用户身份和权限
- 使用事务处理：
  - 添加茶水计分消息记录
  - 更新茶水余额
  - 扣减给茶水计分的玩家分数
- 构建响应和广播数据，包含玩家分数变化信息
- 广播给房间内所有玩家

### 2. 云对象处理函数
**文件**: `uniCloud-alipay/cloudfunctions/room/index.obj.js`

#### 更新分数计算函数
在`_calculatePlayerFinalScores`函数中：
- 添加`tea_score`到查询的消息类型列表
- 添加茶水计分的分数处理逻辑：
  ```javascript
  } else if (message_type === "tea_score") {
    // 处理茶水计分消息
    const amount = detail_data.amount || 0;
    
    if (sender_id && scoreMap.hasOwnProperty(sender_id)) {
      scoreMap[sender_id] = NP.minus(scoreMap[sender_id], Math.abs(amount)); // 给茶水计分者减分
    }
  }
  ```

### 3. 前端计分逻辑
**文件**: `pages/room/mixins/scoringMixin.js`

#### 修改云对象茶水计分处理
在`handleTeaScoreByCloudFunction`方法中：
- 添加玩家分数扣减逻辑：
  ```javascript
  // 扣减当前用户分数
  this.updatePlayerScore(this.realCurrentUserId, -score);
  ```

## 数据流程

### WebSocket模式
1. 用户点击茶水计分确认
2. 前端调用`wsScoreTeaWater(score)`
3. WebSocket发送`tea_score`消息到后端
4. 后端`handleTeaScore`函数处理：
   - 事务中更新茶水余额和玩家分数
   - 添加消息记录
5. 广播给房间内所有玩家：
   - 新消息记录
   - 茶水余额变化
   - 玩家分数变化
6. 前端接收广播，更新UI

### 云对象降级模式
1. 用户点击茶水计分确认
2. 前端调用`handleTeaScoreByCloudFunction(score)`
3. 调用云对象`addRoomMessage("tea_score", {amount: score})`
4. 云对象添加消息记录到数据库
5. 前端手动更新：
   - 茶水余额（通过`handleTeaAmountCollected`）
   - 玩家分数（通过`updatePlayerScore`）

## 广播数据结构

### WebSocket广播数据
```javascript
{
  type: 'tea_score',
  data: {
    newMessage: {
      message_type: "tea_score",
      sender_id: "用户ID",
      detail_data: { amount: 100 },
      // ... 其他消息字段
    },
    teaWaterBalance: 新的茶水余额,
    players: [{
      id: "用户ID",
      scoreChange: -100  // 负数表示扣减
    }]
  }
}
```

### 增量更新处理
前端`websocketMixin.js`中的`handleIncrementalUpdate`方法会处理：
- `incrementalData.players`: 更新玩家分数
- `incrementalData.teaWaterBalance`: 更新茶水余额
- `incrementalData.newMessage`: 添加新消息到列表

## 测试验证

### 功能测试
1. **茶水计分**：给茶水计分100分
   - 茶水余额应该增加100
   - 操作者分数应该减少100
   - 消息列表显示茶水计分记录

2. **多用户同步**：用户A给茶水计分
   - 用户B应该看到茶水余额增加
   - 用户B应该看到用户A分数减少
   - 用户B应该收到广播通知

3. **分数计算**：房间结束时
   - 茶水计分应该正确影响最终分数计算
   - 给茶水计分的玩家最终分数应该包含扣减

### 边界测试
- 分数为0或负数的处理
- 网络异常时的降级处理
- 并发操作的事务安全

## 注意事项
1. **事务安全**：WebSocket模式使用数据库事务确保数据一致性
2. **降级兼容**：云对象模式下手动更新UI状态
3. **分数计算**：茶水计分正确影响玩家最终分数
4. **实时同步**：所有操作都会实时广播给房间内其他玩家
5. **消息记录**：茶水计分会正确记录到消息历史中
