# 茶水计分功能测试指南

## 测试环境准备
1. 确保数据库schema已更新（包含tea_score消息类型）
2. 部署更新后的云函数
3. 确保前端代码已更新

## 测试步骤

### 基础功能测试
1. **茶水按钮点击测试**
   - 进入房间页面
   - 点击左侧茶水按钮
   - 验证：应该弹出"给茶水计分"弹窗，而不是茶水设置弹窗

2. **茶水计分弹窗UI测试**
   - 验证弹窗标题显示为"给茶水计分"
   - 验证显示茶水图标（棕色背景）
   - 验证显示"茶水"文字
   - 验证输入框placeholder为"填写茶水金额"
   - 验证右侧有设置图标按钮

3. **茶水计分功能测试**
   - 在输入框中输入正数（如100）
   - 点击"确定计分"按钮
   - 验证：
     - 弹窗关闭
     - 显示成功提示"已给茶水记分100分"
     - 茶水按钮上的余额数字增加100
     - 消息列表中出现茶水计分记录

4. **设置图标功能测试**
   - 在茶水计分弹窗中点击设置图标
   - 验证：弹出茶水自动设置弹窗
   - 修改茶水设置并保存
   - 验证：设置弹窗关闭，返回茶水计分弹窗

### 边界条件测试
1. **输入验证测试**
   - 输入空值，点击确定 → 应显示"请输入有效的分数"
   - 输入负数，点击确定 → 应显示"请输入有效的分数"
   - 输入非数字，点击确定 → 应显示"请输入有效的分数"
   - 输入0，点击确定 → 应显示"请输入有效的分数"

2. **大数值测试**
   - 输入较大数值（如99999）
   - 验证计分成功，余额正确更新

### 多用户实时同步测试
1. **WebSocket同步测试**
   - 用户A给茶水计分
   - 验证用户B实时看到：
     - 茶水余额更新
     - 收到广播通知"用户A 给茶水记分 XXX"
     - 消息列表中出现新的茶水计分记录

2. **网络异常测试**
   - 断开WebSocket连接
   - 进行茶水计分操作
   - 验证：降级到云对象模式，功能正常

### 兼容性测试
1. **与现有功能兼容性**
   - 验证玩家计分功能正常
   - 验证茶水设置功能正常
   - 验证出分收分功能正常

2. **不同玩法模式测试**
   - 传统记分模式下测试茶水计分
   - 给分玩法模式下测试茶水计分
   - 验证两种模式下功能都正常

## 预期结果

### 成功场景
- 茶水计分操作成功完成
- 茶水余额正确更新
- 实时同步给房间内其他用户
- 消息记录正确保存
- UI状态正确更新

### 错误处理
- 输入验证错误时显示合适的提示
- 网络错误时自动降级处理
- 权限错误时显示相应提示

## 注意事项
1. 茶水计分不涉及茶水抽佣，直接增加茶水余额
2. 茶水计分支持实时同步
3. 设置图标只在茶水计分模式下显示
4. 茶水计分不影响玩家个人分数

## 常见问题排查
1. **弹窗不显示**：检查recordScoreForTea方法是否正确调用
2. **设置图标不显示**：检查modalType是否为'tea'
3. **计分失败**：检查网络连接和权限
4. **余额不更新**：检查WebSocket连接状态
5. **广播不同步**：检查WebSocket广播逻辑
