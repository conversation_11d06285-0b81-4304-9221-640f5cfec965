# 茶水计分UI更新说明

## 更新内容
根据用户反馈，将茶水设置图标从输入框右侧移动到弹窗标题左侧，并且点击标题区域也能打开茶水设置。

## 修改详情

### 1. 标题区域重新设计
- **位置**：弹窗标题左侧
- **图标**：使用设置图标（&#xe87b;）
- **交互**：点击图标或标题文字都能打开茶水设置
- **样式**：hover效果，提供视觉反馈

### 2. 移除原有设置按钮
- 移除了输入框右侧的设置图标
- 简化了输入区域的布局
- 保持输入框的原有功能

### 3. 新的UI结构
```
茶水计分弹窗
├── 标题栏
│   ├── [设置图标] 给茶水计分  ← 可点击区域
│   └── [关闭按钮]
├── 内容区域
│   └── 茶水图标 + 输入框（无设置按钮）
└── 确定按钮
```

## 技术实现

### 1. 使用base-modal的header插槽
```vue
<template #header v-if="modalType === 'tea'">
  <div class="tea-modal-header">
    <div class="tea-title-section" @click="handleTeaSettings">
      <div class="tea-settings-icon">
        <span class="iconfont settings-icon">&#xe87b;</span>
      </div>
      <div class="modal-title">给茶水计分</div>
    </div>
    <div class="modal-close" @click="handleClose">
      <span class="iconfont close-icon">&#xe874;</span>
    </div>
  </div>
</template>
```

### 2. 样式特点
- **可点击区域**：整个标题区域（图标+文字）都可点击
- **视觉反馈**：hover时背景色变化
- **图标大小**：24px容器，14px图标
- **间距**：图标和文字间距适中

### 3. 交互逻辑
- 点击标题区域 → 触发`handleTeaSettings`方法
- 保持原有的茶水设置弹窗逻辑不变
- 关闭按钮功能保持不变

## 用户体验改进

### 优点
1. **更直观**：设置功能在标题区域更容易发现
2. **更简洁**：输入区域更加简洁，专注于输入功能
3. **更一致**：与其他弹窗的设计模式保持一致
4. **更易用**：点击区域更大，更容易操作

### 视觉效果
- 设置图标在标题左侧，与文字对齐
- hover时整个标题区域有背景色反馈
- 图标颜色为次要文字色，不会过于突出
- 保持与现有设计风格的一致性

## 测试要点

### 功能测试
1. 点击设置图标能打开茶水设置弹窗
2. 点击标题文字也能打开茶水设置弹窗
3. 输入框功能正常，无设置按钮干扰
4. 关闭按钮功能正常

### 样式测试
1. 标题区域布局正确
2. hover效果正常显示
3. 图标大小和颜色符合设计
4. 响应式布局正常

### 兼容性测试
1. 非茶水模式的弹窗不受影响
2. 其他计分功能正常工作
3. 茶水设置弹窗功能正常

## 注意事项
- 只有茶水计分模式（modalType === 'tea'）才显示自定义标题
- 其他模式仍使用原有的标题显示方式
- 保持了所有原有功能的完整性
