<template>
  <base-modal
    :visible="visible"
    :title="getModalTitle"
    @close="handleClose"
  >
    <!-- 主体内容 -->
    <div class="players-list-container">
      <scroll-view
        class="players-scroll-view"
        scroll-y="true"
        :show-scrollbar="false"
      >
        <!-- 茶水计分模式 -->
        <div v-if="modalType === 'tea'" class="tea-score-item">
          <div class="player-info-left">
            <div class="player-avatar">
              <div class="avatar function-avatar tea-avatar">
                <span class="iconfont function-icon">&#xe878;</span>
              </div>
            </div>
            <div class="player-name">茶水</div>
          </div>
          <div class="player-input-right">
            <input
              class="amount-input"
              type="digit"
              maxlength="6"
              v-model="teaInputAmount"
              placeholder="填写茶水金额"
              confirm-type="done"
            />
            <div class="tea-settings-icon" @click="handleTeaSettings">
              <span class="iconfont settings-icon">&#xe87b;</span>
            </div>
          </div>
        </div>

        <!-- 普通玩家计分模式 -->
        <div
          v-else
          class="player-score-item"
          v-for="player in displayPlayers"
          :key="player.id"
        >
          <div class="player-info-left">
            <div class="player-avatar">
              <avatar-display :avatarFileId="player.avatarFileId" size="50px" />
            </div>
            <div class="player-name">{{ player.name }}</div>
          </div>
          <div class="player-input-right">
            <input
              class="amount-input"
              type="digit"
              maxlength="6"
              v-model="player.inputAmount"
              placeholder="填写支出金额"
              confirm-type="done"
            />
          </div>
        </div>
      </scroll-view>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button class="confirm-score-button" @click="handleConfirm">
        确定计分
      </button>
    </template>
  </base-modal>
</template>

<script>
import BaseModal from "./base-modal.vue";
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  name: "ScoreModal",
  components: {
    BaseModal,
    AvatarDisplay,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    modalType: {
      type: String,
      default: "single", // 'single', 'multiple' 或 'tea'
    },
    selectedPlayer: {
      type: Object,
      default: null,
    },
    players: {
      type: Array,
      default: () => [],
    },
    currentUserId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      teaInputAmount: "", // 茶水输入金额
    };
  },
  computed: {
    // 弹窗标题
    getModalTitle() {
      if (this.modalType === 'single') {
        return `给 ${this.selectedPlayer?.name} 计分`;
      } else if (this.modalType === 'multiple') {
        return '记录一笔支出';
      } else if (this.modalType === 'tea') {
        return '给茶水计分';
      }
      return '计分';
    },
    displayPlayers() {
      if (this.modalType === "single" && this.selectedPlayer) {
        // 单个玩家计分，只显示选中的玩家
        return [
          {
            ...this.selectedPlayer,
            inputAmount: "",
          },
        ];
      } else if (this.modalType === "multiple") {
        // 多个玩家计分，过滤掉当前用户
        return this.players
          .filter((player) => player.id !== this.currentUserId)
          .map((player) => ({
            ...player,
            inputAmount: "",
          }));
      } else if (this.modalType === "tea") {
        // 茶水计分模式，不需要显示玩家列表
        return [];
      }
      return [];
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleConfirm() {
      if (this.modalType === "single") {
        // 单个玩家计分
        const player = this.displayPlayers[0];
        if (!player.inputAmount || isNaN(Number(player.inputAmount))) {
          uni.showToast({
            title: "请输入有效的分数",
            icon: "none",
          });
          return;
        }

        const score = Number(player.inputAmount);

        // 传递计分数据
        this.$emit("confirm", {
          type: "single",
          player: player,
          score: score,
        });
      } else if (this.modalType === "multiple") {
        // 多个玩家计分
        const validEntries = this.displayPlayers.filter(
          (player) =>
            player.inputAmount &&
            !isNaN(Number(player.inputAmount)) &&
            Number(player.inputAmount) > 0
        );

        if (validEntries.length === 0) {
          uni.showToast({
            title: "请至少为一个玩家输入有效的分数",
            icon: "none",
          });
          return;
        }

        // 传递计分数据
        this.$emit("confirm", {
          type: "multiple",
          entries: validEntries.map((player) => ({
            player: player,
            score: Number(player.inputAmount),
          })),
        });
      } else if (this.modalType === "tea") {
        // 茶水计分
        if (!this.teaInputAmount || isNaN(Number(this.teaInputAmount))) {
          uni.showToast({
            title: "请输入有效的分数",
            icon: "none",
          });
          return;
        }

        const score = Number(this.teaInputAmount);

        // 传递茶水计分数据
        this.$emit("confirm", {
          type: "tea",
          score: score,
        });
      }
    },

    // 处理茶水设置按钮点击
    handleTeaSettings() {
      this.$emit("tea-settings");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 计分内容样式 */
.players-list-container {
  padding: 0 $spacing-lg;
  max-height: calc(70vh - 140px);
  overflow-y: auto;
}

.players-scroll-view {
  flex: 1;
  height: 100%;
  width: 100%;
  padding: $spacing-md;
}

.player-score-item,
.tea-score-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md 0;
}

.player-info-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.player-avatar {
  @include avatar(40px);
}

.player-name {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.player-input-right {
  flex-shrink: 0;
  min-width: 120px;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.amount-input {
  @include input-base;
  text-align: right;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  padding-left: $spacing-base;
  border-radius: $border-radius-base;
  padding-right: $spacing-base;
  width: 100%;
  height: 50px;
  transition: all 0.2s ease;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
  }
}

.confirm-score-button {
  @include button-primary;
  width: 100%;
  height: 44px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-base;

  &:active {
    background-color: $primary-dark;
  }
}

/* 茶水相关样式 */
.tea-avatar {
  background-color: #8B4513;
  color: white;

  .function-icon {
    font-size: 20px;
  }
}

.tea-settings-icon {
  @include icon-container(32px, $background-color);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    background-color: $border-color;
  }

  &:active {
    background-color: $border-light;
  }

  .settings-icon {
    font-size: 16px;
    color: $text-secondary;
  }
}
</style>
