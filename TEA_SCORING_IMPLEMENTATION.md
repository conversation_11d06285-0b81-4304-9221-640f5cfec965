# 茶水计分功能实现总结

## 功能概述
实现了点击茶水按钮弹出计分弹窗的功能，支持手动给茶水增加分数，并在计分弹窗中提供设置图标来访问自动茶水设置。

## 实现的功能
1. **茶水按钮点击事件修改**：将茶水按钮的点击事件从`showTeaSettings`改为`recordScoreForTea`
2. **计分弹窗扩展**：支持茶水计分模式，显示茶水图标和设置按钮
3. **茶水计分逻辑**：添加完整的茶水计分处理流程，支持WebSocket和云对象两种方式
4. **后端支持**：在WebSocket处理函数中添加茶水计分的完整支持
5. **数据库更新**：更新消息类型枚举，支持`tea_score`消息类型

## 修改的文件

### 前端文件
1. **pages/room/index.vue**
   - 修改茶水按钮点击事件：`@click="recordScoreForTea"`
   - 添加茶水设置事件处理：`@tea-settings="showTeaSettings"`

2. **components/score-modal.vue**
   - 添加茶水计分模式支持（modalType: 'tea'）
   - 添加茶水专用UI（茶水图标、设置按钮）
   - 扩展确认逻辑处理茶水计分
   - 添加茶水相关CSS样式

3. **pages/room/mixins/scoringMixin.js**
   - 添加`recordScoreForTea()`方法
   - 扩展`handleScoreConfirm()`支持茶水计分
   - 添加`handleTeaScore()`和`handleTeaScoreByCloudFunction()`方法

4. **pages/room/mixins/websocketMixin.js**
   - 添加`wsScoreTeaWater()`方法
   - 扩展广播通知处理茶水计分
   - 扩展增量更新处理茶水计分消息

### 后端文件
5. **uniCloud-alipay/cloudfunctions/room-websocket/index.js**
   - 添加`tea_score`操作类型处理
   - 实现`handleTeaScore()`函数
   - 支持茶水计分的事务处理和广播

6. **uniCloud-alipay/database/room_messages.schema.json**
   - 更新消息类型枚举，添加`tea_score`

## 功能流程

### 茶水计分流程
1. 用户点击茶水按钮
2. 弹出计分弹窗（茶水模式）
3. 用户输入分数并确认
4. 优先使用WebSocket发送茶水计分请求
5. 后端处理茶水计分，更新茶水余额
6. 广播给房间内其他用户
7. 前端更新茶水余额显示

### 茶水设置流程
1. 在茶水计分弹窗中点击设置图标
2. 弹出茶水自动设置弹窗
3. 用户修改设置并保存
4. 关闭设置弹窗，返回计分弹窗

## 技术特点
1. **双重支持**：同时支持WebSocket实时通信和云对象降级方案
2. **事务安全**：使用数据库事务确保数据一致性
3. **实时广播**：茶水计分操作实时广播给房间内所有用户
4. **UI复用**：复用现有计分弹窗组件，通过模式切换实现不同功能
5. **响应式更新**：茶水余额变化实时反映在UI上

## 消息类型
- `tea_score`：茶水计分消息，包含分数信息
- 消息结构：
  ```json
  {
    "message_type": "tea_score",
    "detail_data": {
      "amount": 100
    }
  }
  ```

## 注意事项
1. 茶水计分不涉及茶水抽佣计算，直接增加茶水余额
2. 茶水计分支持WebSocket实时同步
3. 设置图标只在茶水计分模式下显示
4. 保持与现有计分功能的一致性和兼容性
