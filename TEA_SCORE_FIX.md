# 茶水计分错误修复

## 问题描述
当点击茶水计分的"确定计分"按钮时，出现错误：
```
云对象茶水计分失败: Error: 不支持的消息类型: tea_score
```

## 问题原因
云对象（云函数）中的`addRoomMessage`方法不支持`tea_score`消息类型，具体原因：

1. **消息类型白名单验证**：`addRoomMessage`方法中有消息类型验证，`tea_score`不在允许的类型列表中
2. **消息格式化缺失**：`_formatMessageDetailData`函数中没有处理`tea_score`消息类型的逻辑

## 修复内容

### 1. 添加消息类型支持
在`addRoomMessage`方法中的消息类型白名单中添加`tea_score`：

```javascript
if (
  ![
    "score",
    "system", 
    "settlement",
    "distribute",
    "collect",
    "round_start",
    "round_end",
    "tea_score",  // ← 新增
  ].includes(messageType)
) {
  return {
    code: 400,
    message: "无效的消息类型",
  };
}
```

### 2. 添加消息格式化逻辑
在`_formatMessageDetailData`函数中添加对`tea_score`的处理：

```javascript
case "tea_score":
  return {
    amount: detailData.amount,
  };
```

### 3. 更新方法注释
更新`addRoomMessage`方法的注释，包含所有支持的消息类型。

## 修改的文件
- `uniCloud-alipay/cloudfunctions/room/index.obj.js`

## 修复后的流程
1. 用户点击茶水计分的"确定计分"按钮
2. 前端调用`handleTeaScoreByCloudFunction`方法
3. 调用云对象的`addRoomMessage`方法，传入`tea_score`消息类型
4. 云对象验证消息类型（现在支持`tea_score`）
5. 消息成功添加到数据库
6. 前端更新茶水余额并显示成功提示

## 测试验证
修复后，茶水计分功能应该能够正常工作：
- 点击茶水按钮弹出计分弹窗
- 输入分数并点击确定
- 成功添加茶水计分记录
- 茶水余额正确更新
- 显示成功提示消息

## 注意事项
- 茶水计分不影响玩家个人分数，只影响茶水余额
- 茶水计分消息会正确保存到数据库并显示在消息列表中
- WebSocket模式下的茶水计分不受此问题影响（因为WebSocket有独立的处理逻辑）
- 此修复主要解决云对象降级模式下的茶水计分问题
