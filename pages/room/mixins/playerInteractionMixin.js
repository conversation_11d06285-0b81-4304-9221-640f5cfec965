// 玩家交互 Mixin
// 负责处理玩家头像点击、用户行为和退出房间等交互逻辑

import { showToast } from "@/utils/commonMethods.js";
import { RoomService } from "@/utils/room.js";

export default {
  methods: {
    // ==================== 头像点击处理 ====================
    
    /**
     * 处理头像点击事件
     * @param {Object} player 被点击的玩家对象
     */
    handleAvatarClick(player) {
      if (player.id === this.realCurrentUserId) {
        this.showCurrentUserActionSheet();
      } else {
        this.handleOtherPlayerClick(player);
      }
    },

    /**
     * 处理点击其他玩家的逻辑
     * @param {Object} player 被点击的玩家对象
     */
    handleOtherPlayerClick(player) {
      if (this.isTraditionalView) {
        // 传统记分模式：直接进入计分
        this.recordScoreForPlayer(player);
      } else {
        // 给分玩法：显示其他玩家的流水
        this.showPlayerTransactions(player);
      }
    },

    /**
     * 显示当前用户的操作选择
     */
    showCurrentUserActionSheet() {
      const actionItems = ['查看个人流水', '编辑个人资料'];
      
      uni.showActionSheet({
        itemList: actionItems,
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.viewCurrentUserTransactions();
              break;
            case 1:
              this.showUserProfileEditor();
              break;
          }
        },
        fail: (err) => {
          console.log('ActionSheet 取消或错误:', err);
        }
      });
    },

    /**
     * 查看当前用户的流水记录
     */
    viewCurrentUserTransactions() {
      const currentUser = this.getCurrentUser();
      if (currentUser) {
        this.showPlayerTransactions(currentUser);
      } else {
        showToast("获取用户信息失败", "none");
      }
    },

    // ==================== 退出房间相关 ====================
    
    /**
     * 退出房间
     */
    async exitRoom() {
      // 检查是否有未结算账单（可根据实际业务逻辑调整）
      // if (this.hasNoSettleAccount) {
      //   uni.showModal({
      //     title: "有未结算的账单，请先结算",
      //     icon: "none",
      //     showCancel: false,
      //   });
      //   return;
      // }

      uni.showModal({
        title: "提示",
        content: "确定要退出房间吗？",
        success: async (res) => {
          if (res.confirm) {
            await this.handleExitRoom();
          }
        },
      });
    },

    /**
     * 处理退出房间的实际逻辑
     */
    async handleExitRoom() {
      try {
        // 显示加载提示
        uni.showLoading({
          title: "正在退出房间...",
          mask: true,
        });

        // 获取房间ID
        const roomId = this.roomInfo?.id;
        if (!roomId) {
          uni.hideLoading();
          showToast("房间信息异常，直接返回", "none");
          uni.navigateBack();
          return;
        }

        let result = { success: false };

        // 检查是否启用WebSocket并且连接正常
        if (this.isWebSocketEnabled && this.wsLeaveRoom) {
          console.log('使用WebSocket退出房间');
          
          try {
            // 通过WebSocket发送退出房间请求
            const wsResult = await this.wsLeaveRoom();
            
            if (wsResult.success) {
              result = {
                success: true,
                message: '退出房间成功',
                data: wsResult.data
              };
              console.log('WebSocket退出房间成功');
            } else {
              throw new Error(wsResult.message || 'WebSocket退出房间失败');
            }
          } catch (wsError) {
            console.warn('WebSocket退出房间失败，回退到传统API:', wsError);
            // 继续执行下面的传统API调用
          }
        }

        // 如果WebSocket失败或不可用，使用传统的API
        if (!result.success) {
          console.log('使用传统API退出房间');
          result = await RoomService.leaveRoom(roomId);
        }

        uni.hideLoading();

        if (result.success) {
          // 立即关闭WebSocket连接，避免接收后续消息
          if (this.closeWebSocket) {
            this.closeWebSocket();
          }

          // 清理本地状态
          this.clearRoomData();

          // 清理其他状态（如果存在相关方法）
          if (this.stopHintScrolling) {
            this.stopHintScrolling();
          }
          if (this.releaseWakeLock) {
            this.releaseWakeLock();
          }

          // 显示成功提示
          showToast(result.message || "退出房间成功", "success");

          // 延时返回上一页，确保用户看到提示
          setTimeout(() => {
            uni.navigateBack();
            // 通知首页刷新用户数据
            uni.$emit('refreshUserData');
          }, 1000);
        } else {
          // 退出失败，显示错误信息
          showToast(result.message || "退出房间失败", "none");

          // 如果是权限相关错误，仍然返回上一页
          if (result.code === 403 || result.code === 404) {
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        }
      } catch (error) {
        uni.hideLoading();
        console.error("退出房间异常:", error);
        showToast("退出房间失败，请稍后重试", "none");
      }
    },
  }
}; 